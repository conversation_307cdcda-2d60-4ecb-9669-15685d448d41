import fetch from "node-fetch";
import "dotenv/config";

const url = `https://discord.com/api/v10/applications/${process.env.APP_ID}/commands`;

const commands = [
	{
		name: "hello",
		description: "Say hello!",
		type: 1,
	},
];

for (const command of commands) {
	const res = await fetch(url, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bot ${process.env.TOKEN}`,
		},
		body: JSON.stringify(command),
	});

	console.log(await res.json());
}

import express from "express";
import { verifyKeyMiddleware } from "discord-interactions";
import "dotenv/config";

const app = express();

app.post(
	"/interactions",
	verifyKeyMiddleware(process.env.PUBLIC_KEY),
	(req, res) => {
		const interaction = req.body;

		if (interaction.type === 1) {
			return res.send({ type: 1 }); // PING
		}

		if (interaction.type === 2) {
			return res.send({
				type: 4,
				data: {
					content: `You ran /${interaction.data.name}!`,
				},
			});
		}
	},
);

app.listen(process.env.PORT, () => {
	console.log(`Listening on port ${process.env.PORT}`);
});

import { verifyKey } from "discord-interactions";

export default async function handler(req, res) {
	if (req.method !== "POST") {
		return res.status(405).send("Method not allowed");
	}

	const signature = req.headers["x-signature-ed25519"];
	const timestamp = req.headers["x-signature-timestamp"];
	const body = await getRawBody(req);

	const isValid = verifyKey(
		body,
		signature,
		timestamp,
		process.env.PUBLIC_KEY,
	);
	if (!isValid) {
		return res.status(401).send("Bad request signature");
	}

	const json = JSON.parse(body.toString("utf-8"));

	if (json.type === 1) {
		return res.status(200).json({ type: 1 });
	}

	if (json.type === 2) {
		return res.status(200).json({
			type: 4,
			data: { content: "Hello from Vercel!" },
		});
	}

	res.status(400).end();
}

function getRawBody(req) {
	return new Promise((resolve, reject) => {
		let data = "";
		req.on("data", (chunk) => {
			data += chunk;
		});
		req.on("end", () => resolve(Buffer.from(data)));
		req.on("error", reject);
	});
}
